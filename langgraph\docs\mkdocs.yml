site_name: "LangGraph"
site_description: Build reliable, stateful AI systems, without giving up control
site_url: https://langchain-ai.github.io/langgraph/
repo_url: https://github.com/langchain-ai/langgraph
edit_uri: edit/main/docs/docs/
theme:
  name: material
  custom_dir: overrides
  logo_dark_mode: static/wordmark_light.svg
  logo_light_mode: static/wordmark_dark.svg
  favicon: static/favicon.png
  icon:
    repo: fontawesome/brands/git-alt
  features:
    - announce.dismiss
    - content.code.annotate
    - content.code.copy
    - content.code.select
    - content.tabs.link
    - content.action.edit
    - content.tooltips
    - navigation.indexes
    - navigation.footer
    - navigation.instant
    - navigation.sections
    - navigation.instant.prefetch
    - navigation.instant.progress
    - navigation.path
    - navigation.tabs
    - navigation.top
    - navigation.prune
    - navigation.tracking
    - search.highlight
    - search.share
    - search.suggest
  palette:
    - scheme: default
      primary: white
      accent: gray
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - scheme: slate
      primary: grey
      accent: white
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  font:
    text: "Public Sans"
    code: "Roboto Mono"
plugins:
  - search:
      separator: '[\s\u200b\-,:!=\[\]()"`/]+|\.(?!\d)|&[lg]t;'
  - exclude-search:
      exclude:
        - additional-resources/index.md
        - agents/prebuilt.md
        - cloud/concepts/cron_jobs.md
        - cloud/concepts/data_storage_and_privacy.md
        - cloud/concepts/webhooks.md
        - cloud/deployment/cloud.md
        - cloud/deployment/custom_docker.md
        - cloud/deployment/egress.md
        - cloud/deployment/graph_rebuild.md
        - cloud/deployment/self_hosted_control_plane.md
        - cloud/deployment/self_hosted_data_plane.md
        - cloud/deployment/semantic_search.md
        - cloud/deployment/setup_javascript.md
        - cloud/deployment/setup_pyproject.md
        - cloud/deployment/setup.md
        - cloud/deployment/standalone_container.md
        - cloud/how-tos/add-human-in-the-loop.md
        - cloud/how-tos/background_run.md
        - cloud/how-tos/clone_traces_studio.md
        - cloud/how-tos/configurable_headers.md
        - cloud/how-tos/configuration_cloud.md
        - cloud/how-tos/cron_jobs.md
        - cloud/how-tos/datasets_studio.md
        - cloud/how-tos/enqueue_concurrent.md
        - cloud/how-tos/generative_ui_react.md
        - cloud/how-tos/human_in_the_loop_time_travel.md
        - cloud/how-tos/interrupt_concurrent.md
        - cloud/how-tos/invoke_studio.md
        - cloud/how-tos/iterate_graph_studio.md
        - cloud/how-tos/reject_concurrent.md
        - cloud/how-tos/rollback_concurrent.md
        - cloud/how-tos/same-thread.md
        - cloud/how-tos/stateless_runs.md
        - cloud/how-tos/streaming.md
        - cloud/how-tos/studio/manage_assistants.md
        - cloud/how-tos/studio/quick_start.md
        - cloud/how-tos/studio/run_evals.md
        - cloud/how-tos/threads_studio.md
        - cloud/how-tos/use_stream_react.md
        - cloud/how-tos/use_threads.md
        - cloud/how-tos/webhooks.md
        - cloud/quick_start.md
        - cloud/reference/api/api_ref_control_plane.md
        - cloud/reference/api/api_ref.md
        - cloud/reference/cli.md
        - cloud/reference/env_var.md
        - cloud/reference/langgraph_server_changelog.md
        - cloud/reference/sdk/js_ts_sdk_ref.md
        - concepts/application_structure.md
        - concepts/assistants.md
        - concepts/auth.md
        - concepts/deployment_options.md
        - concepts/double_texting.md
        - concepts/faq.md
        - concepts/langgraph_cli.md
        - concepts/langgraph_cloud.md
        - concepts/langgraph_components.md
        - concepts/langgraph_control_plane.md
        - concepts/langgraph_data_plane.md
        - concepts/langgraph_platform.md
        - concepts/langgraph_self_hosted_control_plane.md
        - concepts/langgraph_self_hosted_data_plane.md
        - concepts/langgraph_server.md
        - concepts/langgraph_standalone_container.md
        - concepts/langgraph_studio.md
        - concepts/plans.md
        - concepts/scalability_and_resilience.md
        - concepts/sdk.md
        - concepts/server-mcp.md
        - concepts/template_applications.md
        - concepts/why-langgraph.md
        - examples/index.md
        - guides/index.md
        - how-tos/auth/custom_auth.md
        - how-tos/auth/openapi_security.md
        - how-tos/autogen-integration.md
        - how-tos/http/custom_lifespan.md
        - how-tos/http/custom_middleware.md
        - how-tos/http/custom_routes.md
        - how-tos/ttl/configure_ttl.md
        - how-tos/use-remote-graph.md
        - index.md
        - reference/index.md
        - snippets/chat_model_tabs.md
        - troubleshooting/errors/GRAPH_RECURSION_LIMIT.md
        - troubleshooting/errors/index.md
        - troubleshooting/errors/INVALID_CHAT_HISTORY.md
        - troubleshooting/errors/INVALID_CONCURRENT_GRAPH_UPDATE.md
        - troubleshooting/errors/INVALID_GRAPH_NODE_RETURN_VALUE.md
        - troubleshooting/errors/INVALID_LICENSE.md
        - troubleshooting/errors/MULTIPLE_SUBGRAPHS.md
        - troubleshooting/studio.md
        - tutorials/auth/add_auth_server.md
        - tutorials/auth/getting_started.md
        - tutorials/auth/resource_auth.md
  - tags
  - include-markdown
  - mkdocstrings:
      custom_templates: templates
      handlers:
        python:
          import:
            - https://docs.python.org/3/objects.inv
            - https://python.langchain.com/api_reference/objects.inv
          options:
            preload_modules:
              - langchain
              - langchain_core
            enable_inventory: true
            members_order: source
            allow_inspection: true
            heading_level: 2
            show_bases: true
            show_source: false 
            summary: true
            inherited_members: true
            selection:
              docstring_style: google
            docstring_section_style: table
            show_root_toc_entry: false
            show_signature: true
            show_signature_annotations: true
            separate_signature: true
            line_length: 60
            show_symbol_type_heading: true
            show_symbol_type_toc: true
            signature_crossrefs: true
            options:
              filters:
                - "!^_"

nav:
  - Get started:
    - index.md
    - Quickstarts:
      - Start with a prebuilt agent: agents/agents.md
      - Build a custom workflow:
          -  concepts/why-langgraph.md
          -  1. Build a basic chatbot: tutorials/get-started/1-build-basic-chatbot.md
          -  2. Add tools: tutorials/get-started/2-add-tools.md
          -  3. Add memory: tutorials/get-started/3-add-memory.md
          -  4. Add human-in-the-loop: tutorials/get-started/4-human-in-the-loop.md
          -  5. Customize state: tutorials/get-started/5-customize-state.md
          -  6. Time travel: tutorials/get-started/6-time-travel.md
      - Run a local server: tutorials/langgraph-platform/local-server.md
    - General concepts:
      - Workflows & agents: tutorials/workflows.md
      - Agent architectures: concepts/agentic_concepts.md

  - Guides:
    - guides/index.md
    - Agent development:
      - Overview: agents/overview.md
      - Run an agent: agents/run_agents.md
    - LangGraph APIs:
      - Graph API:
          - Overview: concepts/low_level.md
          - Use the Graph API: how-tos/graph-api.md
      - Functional API:
          - Overview: concepts/functional_api.md
          - Use the Functional API: how-tos/use-functional-api.md
      - Runtime: concepts/pregel.md
    - Core capabilities:
      - Streaming:
          - Overview: concepts/streaming.md
          - Stream outputs: how-tos/streaming.md
      - Persistence:
          - Overview: concepts/persistence.md
      - Durable execution:
          - Overview: concepts/durable_execution.md
      - Memory:
          - Overview: concepts/memory.md
          - Add memory: how-tos/memory/add-memory.md
      - Context:
          - Add context: agents/context.md
      - Models:
          - Configure model: agents/models.md
      - Tools:
          - Overview: concepts/tools.md
          - Call tools: how-tos/tool-calling.md
      - Human-in-the-loop:
          - Overview: concepts/human_in_the_loop.md
          - Add human intervention: how-tos/human_in_the_loop/add-human-in-the-loop.md
      - Time travel:
          - Overview: concepts/time-travel.md
          - Use time travel: how-tos/human_in_the_loop/time-travel.md
      - Subgraphs:
          - Overview: concepts/subgraphs.md
          - Use subgraphs: how-tos/subgraph.md
      - Multi-agent:
          - Overview: concepts/multi_agent.md
          - Prebuilt implementation: agents/multi-agent.md
          - Custom implementation: how-tos/multi_agent.md
      - MCP:
          - Overview: concepts/mcp.md
          - Use MCP: agents/mcp.md
      - Tracing:
          - Overview: concepts/tracing.md
          - Enable tracing: how-tos/enable-tracing.md
          - Evaluate performance: agents/evals.md

  - Reference:
    - reference/index.md
    - LangGraph:
      - Graphs: reference/graphs.md
      - Functional API: reference/func.md
      - Pregel: reference/pregel.md
      - Checkpointing: reference/checkpoints.md
      - Storage: reference/store.md
      - Caching: reference/cache.md
      - Types: reference/types.md
      - Runtime: reference/runtime.md
      - Config: reference/config.md
      - Errors: reference/errors.md
      - Constants: reference/constants.md
      - Channels: reference/channels.md
    - Prebuilt:
      - Agents: reference/agents.md
      - Supervisor: reference/supervisor.md
      - Swarm: reference/swarm.md
      - MCP Adapters: reference/mcp.md
    - LangGraph Platform:
      - SDK (Python): cloud/reference/sdk/python_sdk_ref.md
      - SDK (JS/TS): https://langchain-ai.github.io/langgraphjs/reference/modules/sdk.html
      - RemoteGraph: reference/remote_graph.md

  - Examples:
    - examples/index.md
    - Template applications: concepts/template_applications.md  # TODO: make tutorial
    - Agentic RAG: tutorials/rag/langgraph_agentic_rag.md
    - Agent Supervisor: tutorials/multi_agent/agent_supervisor.md
    - SQL agent: tutorials/sql/sql-agent.md
    - Prebuilt chat UI: agents/ui.md
    - Graph runs in LangSmith: how-tos/run-id-langsmith.md

  - Additional resources:
    - additional-resources/index.md
    - agents/prebuilt.md # NOTE: prebuilt.md is auto-generated by `make build-prebuilt`
    - LangGraph Academy course: https://academy.langchain.com/courses/intro-to-langgraph
    - Case studies: adopters.md
    - concepts/faq.md
    - llms.txt: llms-txt-overview.md
    - LangChain Forum: https://forum.langchain.com/
    - Troubleshooting:
        - Errors:
          - troubleshooting/errors/index.md
          - troubleshooting/errors/GRAPH_RECURSION_LIMIT.md
          - troubleshooting/errors/INVALID_CONCURRENT_GRAPH_UPDATE.md
          - troubleshooting/errors/INVALID_GRAPH_NODE_RETURN_VALUE.md
          - troubleshooting/errors/MULTIPLE_SUBGRAPHS.md
          - troubleshooting/errors/INVALID_CHAT_HISTORY.md
          - troubleshooting/errors/INVALID_LICENSE.md
    

markdown_extensions:
  - abbr
  - admonition
  - pymdownx.details
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - pymdownx.superfences
  - pymdownx.tabbed:
      alternate_style: true
  - toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
      emoji_index: !!python/name:material.extensions.emoji.twemoji
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      use_pygments: true
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      normalize_issue_symbols: true
      repo_url_shorthand: true
      user: langchain-ai
      repo: langgraph
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.snippets:
      auto_append:
        - includes/mkdocs.md
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
      combine_header_slug: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - github-callouts
hooks:
  - _scripts/notebook_hooks.py
extra:
  social:
    - icon: fontawesome/brands/js
      link: https://langchain-ai.github.io/langgraphjs/
    - icon: fontawesome/brands/github
      link: https://github.com/langchain-ai/langgraph
    - icon: fontawesome/brands/twitter
      link: https://twitter.com/LangChainAI
validation:
  # https://www.mkdocs.org/user-guide/configuration/
  # We are still raising for omitted files because they determine the breadcrumbs for pages.
  omitted_files: info 
  absolute_links: warn
  unrecognized_links: warn
  anchors: warn
  # this is needed to handle headers with anchors for nav
  not_found: info
copyright: >
  Copyright &copy; 2025 LangChain, Inc | <a href="#__consent">Consent Preferences</a>
extra_css:
  - stylesheets/version_admonitions.css
  - stylesheets/logos.css
  - stylesheets/sticky_navigation.css
  - stylesheets/agent_graph_widget.css